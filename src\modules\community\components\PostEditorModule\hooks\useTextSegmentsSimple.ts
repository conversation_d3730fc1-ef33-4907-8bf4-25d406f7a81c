import {useState, useCallback, useRef, useEffect} from 'react';
import {TextSegment, MentionModalState, UserMention} from '../types';
import {
  htmlToSegments,
  mergeAdjacentSegments,
  getFullText,
} from '../utils/textUtils';

export const useTextSegments = (
  initialText: string = '',
  initialHtml: string = '',
) => {
  // Khởi tạo segments từ initialHtml nếu có, nếu không thì từ initialText
  const [segments, setSegments] = useState<TextSegment[]>(() => {
    if (initialHtml) {
      return htmlToSegments(initialHtml);
    }
    return [{text: initialText}];
  });

  const [selection, setSelection] = useState<{start: number; end: number}>({
    start: 0,
    end: 0,
  });

  // Thêm state để theo dõi trạng thái nhập liệu
  const [lastInputChar, setLastInputChar] = useState<string>('');
  const [isComposingVietnamese, setIsComposingVietnamese] = useState<boolean>(false);

  // Mention modal state
  const [mentionModal, setMentionModal] = useState<MentionModalState>({
    visible: false,
    searchText: '',
    users: [],
    loading: false,
    position: {x: 0, y: 0},
  });

  const [currentMentionStart, setCurrentMentionStart] = useState<number>(-1);

  // Refs for editor info
  const editorYRef = useRef<number>(0);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Dummy functions for mention functionality (simplified)
  const checkForMention = useCallback(async (text: string, cursorPosition: number) => {
    // Simplified mention check - just look for @ symbol
    const atIndex = text.lastIndexOf('@', cursorPosition - 1);
    if (atIndex !== -1 && cursorPosition > atIndex) {
      const searchText = text.substring(atIndex + 1, cursorPosition);
      if (searchText.length > 0 && !searchText.includes(' ')) {
        setCurrentMentionStart(atIndex);
        setMentionModal(prev => ({
          ...prev,
          visible: true,
          searchText: searchText,
          loading: true,
        }));
        // In real implementation, you would search for users here
        setTimeout(() => {
          setMentionModal(prev => ({
            ...prev,
            loading: false,
            users: [], // Mock empty users
          }));
        }, 300);
      } else {
        setMentionModal(prev => ({...prev, visible: false}));
      }
    } else {
      setMentionModal(prev => ({...prev, visible: false}));
    }
  }, []);

  // Function để xử lý khi chọn user từ modal
  const selectUserForMention = useCallback((user: UserMention) => {
    // Simplified mention selection
    const fullText = getFullText(segments);
    if (currentMentionStart === -1) return;

    const beforeMention = fullText.substring(0, currentMentionStart);
    const afterMention = fullText.substring(selection.start);
    const newText = beforeMention + user.Name + ' ' + afterMention;

    // Create simple segments
    setSegments([{
      text: newText,
      isMention: false,
      isHashtag: false,
    }]);

    setMentionModal(prev => ({...prev, visible: false}));
    setCurrentMentionStart(-1);
  }, [segments, selection, currentMentionStart]);

  // Simplified selection change handler
  const handleSelectionChange = useCallback((newSelection: any) => {
    if (newSelection) {
      setSelection(newSelection);
    }
  }, []);

  // Simplified onChangeText with debounce
  const onChangeText = useCallback(
    (newText: string) => {
      const fullText = getFullText(segments);

      // Nếu không có thay đổi, không cần xử lý
      if (newText === fullText) return;

      // Đơn giản hóa: tạo segments đơn giản từ text
      const createSimpleSegments = (text: string): TextSegment[] => {
        if (!text) {
          return [{
            text: '',
            isMention: false,
            isHashtag: false,
          }];
        }

        // Xử lý hashtag đơn giản bằng regex
        const parts = text.split(/(#[a-zA-Z0-9_\u00C0-\u024F\u1E00-\u1EFF]+)/);
        const segments: TextSegment[] = [];
        
        parts.forEach(part => {
          if (part) {
            if (part.startsWith('#') && part.length > 1) {
              segments.push({
                text: part,
                isHashtag: true,
                isMention: false,
              });
            } else {
              segments.push({
                text: part,
                isHashtag: false,
                isMention: false,
              });
            }
          }
        });

        return segments.length > 0 ? segments : [{
          text: text,
          isHashtag: false,
          isMention: false,
        }];
      };

      // Sử dụng logic đơn giản
      setSegments(createSimpleSegments(newText));

      // Cập nhật trạng thái
      if (newText.length > 0) {
        setLastInputChar(newText.charAt(newText.length - 1));
      } else {
        setLastInputChar('');
      }
      setIsComposingVietnamese(false);

      // Kiểm tra mention sau khi xử lý text với debounce
      const cursorPosition = selection.start !== undefined ? selection.start : newText.length;

      // Clear previous timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      // Set new timeout for mention check
      debounceTimeoutRef.current = setTimeout(() => {
        checkForMention(newText, cursorPosition).catch(console.error);
      }, 150);
    },
    [segments, selection, checkForMention],
  );

  // Dummy functions for compatibility
  const updateModalPosition = useCallback((editorY: number, cursorPosition: number, text: string) => {
    // Simplified modal positioning
  }, []);

  const updateEditorInfo = useCallback((y: number) => {
    editorYRef.current = y;
  }, []);

  const handleModalSearch = useCallback((searchText: string) => {
    // Simplified search
    setMentionModal(prev => ({
      ...prev,
      searchText,
      loading: true,
    }));
    
    setTimeout(() => {
      setMentionModal(prev => ({
        ...prev,
        loading: false,
        users: [], // Mock empty results
      }));
    }, 300);
  }, []);

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return {
    segments,
    setSegments,
    selection,
    setSelection,
    handleSelectionChange,
    onChangeText,
    lastInputChar,
    isComposingVietnamese,
    mentionModal,
    setMentionModal,
    currentMentionStart,
    setCurrentMentionStart,
    selectUserForMention,
    updateModalPosition,
    updateEditorInfo,
    handleModalSearch,
  };
};
